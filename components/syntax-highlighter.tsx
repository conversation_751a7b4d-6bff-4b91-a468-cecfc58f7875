'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Copy, Check, WrapText, ScrollText } from 'lucide-react';

interface SyntaxHighlighterProps {
  code: string;
  language: string;
  showLineNumbers?: boolean;
  showCopyButton?: boolean;
}

export function SyntaxHighlighter({
  code,
  language,
  showLineNumbers = true,
  showCopyButton = true,
}: SyntaxHighlighterProps) {
  const [copied, setCopied] = useState(false);
  const [highlightedCode, setHighlightedCode] = useState<string>('');
  const [wordWrap, setWordWrap] = useState(false);

  useEffect(() => {
    const loadPrism = async () => {
      // Dynamically import Prism to avoid SSR issues
      const Prism = (await import('prismjs')).default;

      // Import language definitions
      if (language === 'python') {
        await import('prismjs/components/prism-python' as any);
      } else if (language === 'yaml') {
        await import('prismjs/components/prism-yaml' as any);
      } else if (language === 'javascript') {
        await import('prismjs/components/prism-javascript' as any);
      } else if (language === 'typescript') {
        await import('prismjs/components/prism-typescript' as any);
      }

      // Import line numbers plugin if needed
      if (showLineNumbers) {
        await import('prismjs/plugins/line-numbers/prism-line-numbers' as any);
      }

      // Highlight the code
      const highlighted = Prism.highlight(
        code,
        Prism.languages[language] || Prism.languages.plain,
        language
      );

      setHighlightedCode(highlighted);
    };

    loadPrism();
  }, [code, language, showLineNumbers]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const toggleWordWrap = () => {
    setWordWrap(!wordWrap);
  };

  const lines = code.split('\n');

  return (
    <div className="relative">
      {(showCopyButton || true) && (
        <div className="absolute top-2 right-2 z-10 flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleWordWrap}
            title={wordWrap ? "Disable word wrap" : "Enable word wrap"}
          >
            {wordWrap ? (
              <ScrollText className="h-4 w-4" />
            ) : (
              <WrapText className="h-4 w-4" />
            )}
          </Button>

          {showCopyButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={copyToClipboard}
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4 mr-1" />
                  Copied
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </>
              )}
            </Button>
          )}
        </div>
      )}

      <div className="bg-muted rounded-lg overflow-hidden">
        {wordWrap ? (
          // Word wrap mode - no line numbers, just wrapped code
          <div className="overflow-x-auto">
            <pre
              className="p-3 text-[11px] font-mono whitespace-pre-wrap"
              style={{ lineHeight: '1.4' }}
            >
              <code
                className={`language-${language}`}
                dangerouslySetInnerHTML={{ __html: highlightedCode || code }}
              />
            </pre>
          </div>
        ) : (
          // No wrap mode - with line numbers and horizontal scroll
          <div className="flex">
            {showLineNumbers && (
              <div className="bg-muted-foreground/10 px-2 py-3 text-[11px] text-muted-foreground select-none font-mono flex-shrink-0">
                {lines.map((_, index) => (
                  <div
                    key={index}
                    className="text-right min-h-[15.4px] flex items-center justify-end"
                  >
                    {index + 1}
                  </div>
                ))}
              </div>
            )}

            <div className="flex-1 overflow-x-auto">
              <pre
                className="p-3 text-[11px] font-mono whitespace-pre"
                style={{ lineHeight: '1.4' }}
              >
                <code
                  className={`language-${language}`}
                  dangerouslySetInnerHTML={{ __html: highlightedCode || code }}
                />
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
