'use client';

import { useState, useEffect } from 'react';
import { FileTree } from '@/components/file-tree';
import { <PERSON>Viewer } from '@/components/file-viewer';
import { FileSearch } from '@/components/file-search';
import { FileNode } from '@/lib/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, SheetContent, SheetTrigger, SheetTitle, SheetHeader, SheetDescription } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { Menu, BookOpen } from 'lucide-react';

export default function Home() {
  const [files, setFiles] = useState<FileNode[]>([]);
  const [selectedFile, setSelectedFile] = useState<string | undefined>();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    // Load files on component mount
    const loadFiles = async () => {
      try {
        const response = await fetch('/api/files/structure');
        if (response.ok) {
          const data = await response.json();
          setFiles(data);
        } else {
          console.error('Failed to load files:', response.statusText);
          setFiles([]);
        }
      } catch (error) {
        console.error('Failed to load files:', error);
        setFiles([]);
      }
    };

    loadFiles();
  }, []);

  const handleFileSelect = (filePath: string) => {
    setSelectedFile(filePath);
    setSidebarOpen(false); // Close mobile sidebar when file is selected
  };

  const sidebarContent = (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-border">
        <div className="flex items-center gap-2 mb-4">
          <BookOpen className="h-6 w-6 text-primary" />
          <h1 className="text-lg font-semibold">Documentation</h1>
        </div>
        <FileSearch
          onFileSelect={handleFileSelect}
        />
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4">
            <FileTree
              nodes={files}
              selectedFile={selectedFile}
              onFileSelect={handleFileSelect}
            />
          </div>
        </ScrollArea>
      </div>
    </div>
  );

  return (
    <div className="h-screen bg-background">
      {/* Mobile Header */}
      <div className="md:hidden flex items-center gap-2 p-4 border-b bg-muted/30">
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="sm">
              <Menu className="h-4 w-4" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80 p-0">
            <SheetHeader className="sr-only">
              <SheetTitle>Documentation Navigation</SheetTitle>
              <SheetDescription>
                Browse and search through documentation files organized by category
              </SheetDescription>
            </SheetHeader>
            {sidebarContent}
          </SheetContent>
        </Sheet>
        <div className="flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-primary" />
          <h1 className="font-semibold">Documentation</h1>
        </div>
      </div>

      {/* Desktop Layout with Resizable Panels */}
      <div className="hidden md:block h-full">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          <ResizablePanel defaultSize={25} minSize={20} maxSize={40} className="bg-muted/30">
            {sidebarContent}
          </ResizablePanel>
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={75} minSize={60}>
            <FileViewer filePath={selectedFile} />
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Mobile Content */}
      <div className="md:hidden" style={{ height: 'calc(100vh - 73px)' }}>
        <FileViewer filePath={selectedFile} />
      </div>
    </div>
  );
}
